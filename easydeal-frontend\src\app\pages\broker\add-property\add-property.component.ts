import { Component, OnInit, ChangeDetectorRef, Type } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { PropertyData } from 'src/app/models/property.model';
import { PropertyService } from '../services/property.service';
import Swal from 'sweetalert2';
@Component({
  selector: 'app-add-property',
  templateUrl: './add-property.component.html',
  styleUrl: './add-property.component.scss',
})
export class AddPropertyComponent implements OnInit {
  totalSteps = 5;
  currentStep = 0;
  selectedCityId: any;
  selectedCityName: string;
  selectedAreaName: string;
  selectedUnitType: string;
  cities: any[] = [];
  unitTypes: { key: string; value: string }[] = [];
  areas: any[] = [];
  isLoadingCities = false;

  otherAccessoriesList = [
  { key: 'GARAGE', value: 'garage' },
  { key: 'CLUBHOUSE', value: 'clubhouse' },
  { key: 'CLUB', value: 'club' },
  { key: 'STORAGE', value: 'storage' },
  { key: 'ELEVATOR', value: 'elevator' },
  { key: 'SWIMMING POOL', value: 'swimming_pool' },
  { key: 'ALL THE ABOVE', value: 'all_the_above_are_suitable' }
];


  //get brokerId from session
  brokerId: number;

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

  floorTypes: { key: string; value: string }[] = [
    { key: 'Ground', value: 'ground' },
    { key: 'Last Floor', value: 'last_floor' },
    { key: 'Repeated', value: 'repeated' },
    { key: 'All Of The Above', value: 'all_the_above_are_suitable' },
  ];

  viewTypes: { key: string; value: string }[] = [
    { key: 'Water View', value: 'water_view' },
    { key: 'Gardens And Landscape', value: 'gardens_and_landscape' },
    { key: 'Street', value: 'street' },
    { key: 'Entertainment Area', value: 'entertainment_area' },
    { key: 'Garden  ', value: 'garden' },
    { key: 'Main Street', value: ' main_street' },
    { key: 'Square', value: 'square' },
    { key: 'Side Street', value: 'side_street' },
    { key: 'Rear View', value: 'rear_view' },
  ];

  deliveryTypes: { key: string; value: string }[] = [
    { key: 'Immediate Delivery', value: 'immediate_delivery' },
    { key: 'Under Construction', value: 'under_construction' },
  ];

  activityTypes: { key: string; value: string }[] = [
    { key: 'Administrative Only', value: 'administrative_only' },
    { key: 'Commercial Only', value: 'commercial_only' },
    { key: 'Medical Only', value: 'medical_only' },
    {
      key: 'Administrative And Commercial',
      value: 'administrative_and_commercial',
    },
    {
      key: 'Administrative Commercial And Medical',
      value: 'administrative_commercial_and_medical',
    },
  ];

  fitOutConditionTypes: { key: string; value: string }[] = [
    { key: 'Unfitted', value: 'unfitted' },
    { key: 'Fully Fitted', value: 'fully_fitted' },
    { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
  ];

  furnishingStatusTypes: { key: string; value: string }[] = [
    { key: 'Unfurnished', value: 'unfurnished' },
    {
      key: 'Furnished With Air Conditioners',
      value: 'furnished_with_air_conditioners',
    },
    {
      key: 'Furnished Without Air Conditioners',
      value: 'furnished_without_air_conditioners',
    },
  ];

  groundLayoutStatusTypes: { key: string; value: string }[] = [
    { key: 'Vacant Land', value: 'vacant_land' },
    { key: 'Under Construction', value: 'under_construction' },
    { key: 'Fully Built', value: 'fully_built' },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  unitDesignTypes: { key: string; value: string }[] = [
    { key: 'Custom Design', value: 'custom_design' },
    { key: 'One Apartment Per Floor', value: 'one_apartment_per_floor' },
    { key: 'Two Apartments Per Floor', value: 'two_apartments_per_floor' },
    {
      key: 'More Than Two Apartments Per Floor',
      value: 'more_than_two_apartments_per_floor',
    },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  otherAccessoriesTypes: { key: string; value: string }[] = [
    { key: 'Garage', value: 'garage' },
    { key: 'Clubhouse', value: 'clubhouse' },
    { key: 'Club', value: 'club' },
    { key: 'Storage', value: 'storage' },
    { key: 'Elevator', value: 'elevator' },
    { key: 'Swimming Pool', value: 'swimming_pool' },
  ];

  selectedAccessories: string[] = [];

  paymentTypes: { key: string; value: string }[] = [
    { key: 'Cash', value: 'cash' },
    { key: 'Installment', value: 'installment' },
    {
      key: 'All Of The Above Are Suitable ',
      value: 'all_of_the_above_are_suitable',
    },
  ];

  legalTypes: { key: string; value: string }[] = [
    { key: 'Licensed', value: 'licensed' },
    { key: 'Reconciled', value: 'reconciled' },
    { key: 'Reconciliation Required', value: 'reconciliation_required' },
  ];

  financialStatusTypes: { key: string; value: string }[] = [
    { key: 'paid_in_full ', value: 'paid_in_full' },
    { key: 'partially_paid_with_remaining_installments ', value: 'partially_paid_with_remaining_installments' },

  ];

  // Step 0 options
  compoundOptions: { key: string; value: string }[] = [
    { key: 'Outside Compound', value: 'outside_compound' },
    { key: 'Inside Compound', value: 'inside_compound' },
  ];


  // All unit types for filtering
  allUnitTypes: { key: string; value: string }[] = [];

  // Unit types for outside compound
  outsideCompoundUnitTypes: { key: string; value: string }[] = [
    // Residential
    { key: 'Apartments', value: 'apartments' },
    { key: 'Duplexes', value: 'duplexes' },
    { key: 'Studios', value: 'studios' },
    { key: 'Penthouses', value: 'penthouses' },
    { key: 'Basement', value: 'basement' },
    { key: 'Roofs', value: 'roofs' },
    { key: 'Standalone Villas', value: 'standalone_villas' },
    { key: 'Residential Buildings', value: 'residential_buildings' },

    // Commercial/Administrative
    { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },
    { key: 'Administrative Units', value: 'administrative_units' },
    { key: 'Medical Clinics', value: 'medical_clinics' },
    { key: 'Pharmacies', value: 'pharmacies' },
    { key: 'Commercial Stores', value: 'commercial_stores' },

    // Industrial
    { key: 'Warehouses', value: 'warehouses' },
    { key: 'Factories', value: 'factories' },

    // Lands
    { key: 'Residential Villa Lands', value: 'residential_villa_lands' },
    { key: 'Residential Buildings Lands', value: 'residential_buildings_lands' },
    { key: 'Administrative Lands', value: 'administrative_lands' },
    { key: 'Commercial Lands', value: 'commercial_lands' },
    { key: 'Medical Lands', value: 'medical_lands' },
    { key: 'Mixed Lands', value: 'mixed_lands' },
    { key: 'Warehouses Land', value: 'warehouses_land' },
    { key: 'Factory Lands', value: 'factory_lands' },
  ];

  // Unit types for inside compound (will be loaded from API)
  insideCompoundUnitTypes: { key: string; value: string }[] = [];

  // Filtered unit types based on compound selection
  filteredUnitTypes: { key: string; value: string }[] = [];

  step0Form: FormGroup;
  step1Form: FormGroup;
  step2Form: FormGroup;
  step3Form: FormGroup;
  step4Form: FormGroup;
  step5Form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.brokerId = 5;
    this.initForms();
    this.loadUnitTypes();
    this.loadCities();
    this.loadAreas();

    // Initialize filtered unit types as empty
    this.filteredUnitTypes = [];
  }

  initForms() {
    // Step 0: Property Category Selection
    this.step0Form = this.fb.group({
      compoundType: ['', [Validators.required]],

      type: ['', [Validators.required]], // Unit Type moved to step 0
    });

    // Step 1: Basic Property Settings
    this.step1Form = this.fb.group({
      cityId: ['', [Validators.required]],
      areaId: ['', [Validators.required]],
      mallName: ['', [Validators.maxLength(255)]], // Mall name field (optional)
      detailedAddress: ['', [Validators.required, Validators.maxLength(255)]],
      location: ['', [Validators.required, Validators.pattern('https?://.+')]],
      ownerName: ['', Validators.required],
      ownerPhone: [
        '',
        [Validators.required, Validators.pattern('^01[0-2,5]{1}[0-9]{8}$')],
      ],
    });

    // Step 2: Unit Information
    this.step2Form = this.fb.group({
      buildingNumber: ['', [Validators.maxLength(50)]],
      unitNumber: ['', [Validators.maxLength(50)]],
      floor: ['', [Validators.required]],
      unitArea: [
        '',
        [
          Validators.required,
          Validators.min(1),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      buildingArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],
      groundArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],
      numberOfRooms: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      numberOfBathrooms: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      numberOfFloors: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      unitFacing: [''],
      view: ['', [Validators.required]],
      finishingType: ['', [Validators.required]],
      fitOutCondition: [''],
      furnishingStatus: [''],
      groundLayoutStatus: [''],
      unitDesign: [''],
      activity: [''],
      deliveryStatus: ['', [Validators.required]],
      deliveryDate: [''],
      financialStatus: [''], // Financial status field
      otherAccessories: [''], // Optional field - no validators to avoid blocking navigation
      legalStatus: [''], // Moved from step5Form
      unitDescription: ['', [Validators.maxLength(1000)]], // Unit description field
    });

    // Step 3: Financial Information
    this.step3Form = this.fb.group({
      paymentSystem: ['', Validators.required],
      pricePerMeterInInstallment: ['', [Validators.min(0)]],
      totalPriceInInstallment: ['', [Validators.min(0)]],
      pricePerMeterInCash: ['', [Validators.min(0)]],
      totalPriceInCash: ['', [Validators.min(0)]],
    });

    // Step 4: Project Documents
    this.step4Form = this.fb.group({
      diagram: [[]],
      layout: [[]],
      videos: [[]],
      locationInMasterPlan: [[]],
    });

    // Step 5: Owner Information
    this.step5Form = this.fb.group({
      // legalStatus moved to step2Form
    });
  }

  // Get current form based on step
  getCurrentForm(): FormGroup {
    switch (this.currentStep) {
      case 0:
        return this.step0Form;
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      case 3:
        return this.step3Form;
      case 4:
        return this.step4Form;
      case 5:
        return this.step5Form;
      default:
        return this.step0Form;
    }
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.allUnitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        this.insideCompoundUnitTypes = this.allUnitTypes; // API data for inside compound
        console.log('Raw API Response:', this.allUnitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  // Filter unit types based on compound selection
  filterUnitTypes(): void {
    const compoundType = this.step0Form.get('compoundType')?.value;

    if (compoundType === 'outside_compound') {
      this.filteredUnitTypes = this.outsideCompoundUnitTypes;
    } else if (compoundType === 'inside_compound') {
      this.filteredUnitTypes = this.insideCompoundUnitTypes;
    } else {
      this.filteredUnitTypes = [];
    }

    // Clear unit type selection when compound type changes
    this.step0Form.patchValue({ type: '' });
    this.selectedUnitType = '';

    // Trigger change detection to update button state
    this.cdr.detectChanges();
  }

  loadCities(): void {
    this.isLoadingCities = true;
    this.propertyService.getCities().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.isLoadingCities = false;
        this.cdr.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  //**************************************************************** */

// STEP 2
getFieldsToShow(): any[] {
  const compoundType = this.step0Form.get('compoundType')?.value;
  const type = this.step0Form.get('type')?.value;

  // For outside compound apartments
  if (compoundType === 'outside_compound' &&  (type === 'apartments' ||type === 'duplexes' || type === 'studios' || type === 'penthouses'|| type === 'roofs'|| type === 'basement')) {
    return ['buildingNumber', 'unitNumber', 'floor', 'unitArea' ,'numberOfRooms' , 'numberOfBathrooms',  'unitFacing', 'view', 'finishingType','deliveryStatus', 'financialStatus', 'legalStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }
  else if (compoundType === 'outside_compound' &&  (type === 'villas' || type ==='full_buildings')) {
    return ['buildingNumber', 'numberOfFloors', 'buildingArea ',' groundArea' , ' unitDescription ' , ' unitDesign ',  'unitFacing', 'view', 'finishingType',  'legalStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }
  else if (compoundType === 'outside_compound' &&  (type === 'pharmacies' ||type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores' )) {
   return ['mallName','buildingNumber', 'floor', 'unitArea' ,   'view', 'finishingType','deliveryStatus','activity' ,'legalStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }
  else if (compoundType === 'outside_compound' &&  (type === 'warehouses' || type === 'factories' )) {
  return [ 'buildingNumber', 'numberOfFloors', 'groundArea' , 'buildingArea',   'activity' ,'finishingStatus', 'unitDescription ','legalStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }
  else if (compoundType === 'outside_compound' &&  (type === 'residential_villa_lands' || type === 'residential_buildings_lands' || type === 'administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'factory_lands'   )) {
return [ 'unitNumber','buildingNumber', 'unitDescription','activity' , 'unitFacing', 'legalStatus','financialStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }

  return [ ];
}

// Check if a specific field should be shown
shouldShowField(fieldName: string): boolean {
  return this.getFieldsToShow().includes(fieldName);
}


  // /*/***************************** */
  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.step1Form.patchValue({
      cityId: cityId,
    });
    this.loadAreas(cityId);
  }

  selectUnitType(UnitValue: string) {
    this.selectedUnitType = UnitValue;
    this.step0Form.patchValue({
      type: UnitValue,
    });

    // Clear unitFacing field if unit type doesn't require it
    const unitTypesWithFacing = [
      'apartments',
      'duplexes',
      'studios',
      'penthouses',
    ];
    if (!unitTypesWithFacing.includes(UnitValue)) {
      this.step2Form.patchValue({
        unitFacing: null,
      });
    }

    // Clear buildingArea and groundArea fields if unit type doesn't require them
    const unitTypesWithAreaFields = [
      'standalone_villas',
      'factory_lands',
      'commercial_administrative_buildings',
      'residential_buildings',
      'warehouses',

    ];
    if (!unitTypesWithAreaFields.includes(UnitValue)) {
      this.step2Form.patchValue({
        buildingArea: null,
        groundArea: null,
      });
    }

    // Clear activity field if unit type doesn't require it
    const unitTypesWithActivity = [
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
    ];
    if (!unitTypesWithActivity.includes(UnitValue)) {
      this.step2Form.patchValue({
        activity: null,
      });
    }

    // Clear groundLayoutStatus field if unit type doesn't require it
    const unitTypesWithGroundLayout = [
      'factory_lands',
      'warehouses',
      'residential_buildings',
      'commercial_administrative_buildings',
    ];
    if (!unitTypesWithGroundLayout.includes(UnitValue)) {
      this.step2Form.patchValue({
        groundLayoutStatus: null,
      });
    }

    // Clear unitDesign field if unit type doesn't require it
    const unitTypesWithUnitDesign = ['standalone_villas'];
    if (!unitTypesWithUnitDesign.includes(UnitValue)) {
      this.step2Form.patchValue({
        unitDesign: null,
      });
    }

    // Clear fitOutCondition field if unit type doesn't require it
    const unitTypesWithFitOutCondition = [
      'pharmacies',
      'factory_lands',
      'warehouses',
      'commercial_stores',
      'commercial_administrative_buildings',
    ];
    if (!unitTypesWithFitOutCondition.includes(UnitValue)) {
      this.step2Form.patchValue({
        fitOutCondition: null,
      });
    }

    // Clear furnishingStatus field if unit type doesn't require it
    const unitTypesToHideFurnishing = [
      'pharmacies',
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
      'administrative_units',
    ];
    if (unitTypesToHideFurnishing.includes(UnitValue)) {
      this.step2Form.patchValue({
        furnishingStatus: null,
      });
    }

    // Clear legalStatus field if unit type doesn't require it
    const unitTypesWithLegalStatus = [
      'duplexes',
      'penthouses',
      'basement',
      'roofs',
    ];
    if (!unitTypesWithLegalStatus.includes(UnitValue)) {
      this.step2Form.patchValue({
        legalStatus: null,
      });
    }
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaName = areaName;
    this.step1Form.patchValue({
      areaId: areaId,
    });
  }

  // dropdown values for step 2
  selectStep2Value(fieldName: string, value: string) {
    this.step2Form.patchValue({
      [fieldName]: value,
    });
  }

  //dropdown values for step 3
  selectStep3Value(fieldName: string, value: string) {
    this.step3Form.patchValue({
      [fieldName]: value,
    });

    // Clear price fields when payment system changes
    if (fieldName === 'paymentSystem') {
      this.clearPriceFields();
    }
  }

  // Clear all price fields when payment system changes
  clearPriceFields() {
    this.step3Form.patchValue({
      pricePerMeterInCash: null,
      totalPriceInCash: null,
      pricePerMeterInInstallment: null,
      totalPriceInInstallment: null,
    });
  }

  // Check if cash price fields should be displayed
  shouldShowCashFields(): boolean {
    const paymentSystem = this.step3Form.get('paymentSystem')?.value;
    return (
      paymentSystem === 'cash' ||
      paymentSystem === 'all_of_the_above_are_suitable'
    );
  }

  // Check if installment price fields should be displayed
  shouldShowInstallmentFields(): boolean {
    const paymentSystem = this.step3Form.get('paymentSystem')?.value;
    return (
      paymentSystem === 'installment' ||
      paymentSystem === 'all_of_the_above_are_suitable'
    );
  }

  // Check if unitFacing field should be displayed
  shouldShowUnitFacingField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithFacing = [
      'apartments',
      'duplexes',
      'studios',
      'penthouses',
    ];
    return unitTypesWithFacing.includes(unitType);
  }

  // Check if buildingArea and groundArea fields should be displayed
  shouldShowAreaFields(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithAreaFields = [
      'standalone_villas',
      'factory_lands',
      'commercial_administrative_buildings',
      'residential_buildings',
      'warehouses',
    ];
    return unitTypesWithAreaFields.includes(unitType);
  }

  // Check if groundLayoutStatus field should be displayed
  shouldShowGroundLayoutStatusField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithGroundLayout = [
      'factory_lands',
      'warehouses',
      'residential_buildings',
      'commercial_administrative_buildings',
    ];
    return unitTypesWithGroundLayout.includes(unitType);
  }

  // Check if activity field should be displayed
  shouldShowActivityField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithActivity = [
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
    ];
    return unitTypesWithActivity.includes(unitType);
  }

  // Check if unitDesign field should be displayed
  shouldShowUnitDesignField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithUnitDesign = ['standalone_villas'];
    return unitTypesWithUnitDesign.includes(unitType);
  }

  // Check if legalStatus field should be displayed
  shouldShowLegalStatusField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithLegalStatus = [
      'duplexes',
      'penthouses',
      'basement',
      'roofs',
    ];
    return unitTypesWithLegalStatus.includes(unitType);
  }

  // Check if fitOutCondition field should be displayed
  shouldShowFitOutConditionField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithFitOutCondition = [
      'pharmacies',
      'factory_lands',
      'warehouses',
      'commercial_stores',
      'commercial_administrative_buildings',
    ];
    return unitTypesWithFitOutCondition.includes(unitType);
  }

  // Check if furnishingStatus field should be displayed
  shouldShowFurnishingStatusField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesToHideFurnishing = [
      'pharmacies',
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
      'administrative_units',
    ];
    return !unitTypesToHideFurnishing.includes(unitType);
  }

  // dropdown values for step 0
  selectStep0Value(fieldName: string, value: string) {
    console.log('selectStep0Value called:', { fieldName, value });

    this.step0Form.patchValue({
      [fieldName]: value,
    });

    // Filter unit types when compound type changes
    if (fieldName === 'compoundType') {
      this.filterUnitTypes();
    }

    // Handle unit type selection
    if (fieldName === 'type') {
      this.selectedUnitType = value;
      this.selectUnitType(value); // Call existing logic for unit type selection
    }

    console.log('Step0 form after update:', this.step0Form.value);

    // Trigger change detection to update button state
    this.cdr.detectChanges();
  }

  //dropdown values for step 5
  selectStep5Value(fieldName: string, value: string) {
    this.step5Form.patchValue({
      [fieldName]: value,
    });
  }

  submitForm(checkAd : boolean) {
    if (this.isCurrentFormValid()) {
      const formData: PropertyData = {
        ...this.step0Form.value,
        ...this.step1Form.value,
        ...this.step2Form.value,
        ...this.step3Form.value,
        ...this.step5Form.value,
      };
      // console.log(formData);
      const httpFormData = new FormData();

      // Add step0 form data
      Object.keys(this.step0Form.value).forEach((key) => {
        httpFormData.append(key, this.step0Form.value[key]);
      });

      // Add step1 form data
      Object.keys(this.step1Form.value).forEach((key) => {
        httpFormData.append(key, this.step1Form.value[key]);
      });

      // Fields to be included in additionalDetails array
      const additionalDetailsFields = ['numberOfFloors'];

      // Get unit type for conditional field inclusion
      const unitType = this.step0Form.get('type')?.value;

      // Add buildingArea and groundArea to additionalDetails only for specific unit types
      const unitTypesWithAreaFields = [
        'standalone_villas',
        'factory_lands',
        'commercial_administrative_buildings',
        'residential_buildings',
        'warehouses',
      ];
      if (unitTypesWithAreaFields.includes(unitType)) {
        additionalDetailsFields.push('groundArea');
        additionalDetailsFields.push('buildingArea');
      }

      // Add activity to additionalDetails only for specific unit types
      const unitTypesWithActivity = [
        'commercial_stores',
        'factory_lands',
        'warehouses',
        'commercial_administrative_buildings',
      ];
      if (unitTypesWithActivity.includes(unitType)) {
        additionalDetailsFields.push('activity');
      }

      // Add groundLayoutStatus to additionalDetails only for specific unit types
      const unitTypesWithGroundLayout = [
        'factory_lands',
        'warehouses',
        'residential_buildings',
        'commercial_administrative_buildings',
      ];
      if (unitTypesWithGroundLayout.includes(unitType)) {
        additionalDetailsFields.push('groundLayoutStatus');
      }

      // Add unitDesign to additionalDetails only for specific unit types
      const unitTypesWithUnitDesign = ['standalone_villas'];
      if (unitTypesWithUnitDesign.includes(unitType)) {
        additionalDetailsFields.push('unitDesign');
      }

      // Add fitOutCondition to additionalDetails only for specific unit types
      const unitTypesWithFitOutCondition = [
        'pharmacies',
        'factory_lands',
        'warehouses',
        'commercial_stores',
        'commercial_administrative_buildings',
      ];
      if (unitTypesWithFitOutCondition.includes(unitType)) {
        additionalDetailsFields.push('fitOutCondition');
      }

      // Add furnishingStatus to additionalDetails only for specific unit types
      const unitTypesToHideFurnishing = [
        'pharmacies',
        'commercial_stores',
        'factory_lands',
        'warehouses',
        'commercial_administrative_buildings',
        'administrative_units',
      ];
      if (!unitTypesToHideFurnishing.includes(unitType)) {
        additionalDetailsFields.push('furnishingStatus');
      }

      // Add unitFacing to additionalDetails only for specific unit types
      const unitTypesWithFacing = [
        'apartments',
        'duplexes',
        'studios',
        'penthouses',
      ];
      if (unitTypesWithFacing.includes(unitType)) {
        additionalDetailsFields.push('unitFacing');
      }

      // Add step2 form data (excluding fields that go to additionalDetails and otherAccessories)
      Object.keys(this.step2Form.value).forEach((key) => {
        if (
          key !== 'otherAccessories' &&
          !additionalDetailsFields.includes(key)
        ) {
          httpFormData.append(key, this.step2Form.value[key]);
        }
      });

      // Add step3 form data (conditionally based on payment system)
      const paymentSystem = this.step3Form.get('paymentSystem')?.value;

      // Always add payment system
      httpFormData.append('paymentSystem', paymentSystem);

      // Conditionally add price fields based on payment system
      if (paymentSystem === 'cash') {
        // Only send cash price fields
        const pricePerMeterInCash = this.step3Form.get(
          'pricePerMeterInCash'
        )?.value;
        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;

        if (pricePerMeterInCash) {
          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);
        }
        if (totalPriceInCash) {
          httpFormData.append('totalPriceInCash', totalPriceInCash);
        }
      } else if (paymentSystem === 'installment') {
        // Only send installment price fields
        const pricePerMeterInInstallment = this.step3Form.get(
          'pricePerMeterInInstallment'
        )?.value;
        const totalPriceInInstallment = this.step3Form.get(
          'totalPriceInInstallment'
        )?.value;

        if (pricePerMeterInInstallment) {
          httpFormData.append(
            'pricePerMeterInInstallment',
            pricePerMeterInInstallment
          );
        }
        if (totalPriceInInstallment) {
          httpFormData.append(
            'totalPriceInInstallment',
            totalPriceInInstallment
          );
        }
      } else if (paymentSystem === 'all_of_the_above_are_suitable') {
        // Send all price fields
        const pricePerMeterInCash = this.step3Form.get(
          'pricePerMeterInCash'
        )?.value;
        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;
        const pricePerMeterInInstallment = this.step3Form.get(
          'pricePerMeterInInstallment'
        )?.value;
        const totalPriceInInstallment = this.step3Form.get(
          'totalPriceInInstallment'
        )?.value;

        if (pricePerMeterInCash) {
          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);
        }
        if (totalPriceInCash) {
          httpFormData.append('totalPriceInCash', totalPriceInCash);
        }
        if (pricePerMeterInInstallment) {
          httpFormData.append(
            'pricePerMeterInInstallment',
            pricePerMeterInInstallment
          );
        }
        if (totalPriceInInstallment) {
          httpFormData.append(
            'totalPriceInInstallment',
            totalPriceInInstallment
          );
        }
      }

      // Add step5 form data (excluding legalStatus which goes to additionalDetails)
      Object.keys(this.step5Form.value).forEach((key) => {
        if (key !== 'legalStatus') {
          httpFormData.append(key, this.step5Form.value[key]);
        }
      });

      // Create additionalDetails object
      const additionalDetails: any = {};

      // Add fields from step2Form
      additionalDetailsFields.forEach((field) => {
        const value = this.step2Form.get(field)?.value;
        if (value) {
          additionalDetails[field] = value;
        }
      });

      // Add legalStatus from step5Form only for specific unit types
      const unitTypesWithLegalStatus = [
        'duplexes',
        'penthouses',
        'basement',
        'roofs',
      ];
      if (unitTypesWithLegalStatus.includes(unitType)) {
        const legalStatus = this.step5Form.get('legalStatus')?.value;
        if (legalStatus) {
          additionalDetails['legalStatus'] = legalStatus;
        }
      }

      // Send additionalDetails as individual form fields (not JSON)
      Object.keys(additionalDetails).forEach((key) => {
        httpFormData.append(
          `additionalDetails[${key}]`,
          additionalDetails[key]
        );
      });

      //add files
      const fileFields = [
        'diagram',
        'layout',
        'videos',
        'locationInMasterPlan',
      ];
      fileFields.forEach((field) => {
        const files = this.step4Form.get(field)?.value;
        if (files && files.length) {
          const isMultiple = ['layout', 'videos'].includes(field);

          if (isMultiple) {
            files.forEach((file: File) => {
              httpFormData.append(`${field}[]`, file);
            });
          } else {
            httpFormData.append(field, files[0]);
          }
        }
      });

      // Handle otherAccessories as array
      const accessoriesRaw = this.step2Form.get('otherAccessories')?.value;
      const accessoriesArray = Array.isArray(accessoriesRaw)
        ? accessoriesRaw
        : [];

      // Send otherAccessories as individual array elements
      accessoriesArray.forEach((accessory, index) => {
        httpFormData.append(`otherAccessories[${index}]`, accessory);
      });

      httpFormData.append('brokerId', this.brokerId.toString());

      // Set as advertisement
      if(checkAd){
        httpFormData.append('isAdvertisement', '1');
      }

      // Show loading state
      const button = document.querySelector('.btn-primary');
      if (button) {
        button.classList.add('btn-loading');
      }

      this.propertyService.createProperty(httpFormData).subscribe({
        next: async (response) => {
          console.log('Property data submitted:', response);
          await Swal.fire('Property data submitted:', '', response.status);

          this.router.navigate(['/broker/dataandproperties'], {
            queryParams: { success: 'add' },
          });
        },
        error: (err) => {
          console.error('Error loading unitTypes:', err);
          Swal.fire(err.message, '', err.status);

          // Remove loading state
          if (button) {
            button.classList.remove('btn-loading');
          }
        },
        complete: () => {
          this.cdr.detectChanges();
          // Remove loading state
          if (button) {
            button.classList.remove('btn-loading');
          }
        },
      });
    }
  }

  cancel() {
    this.router.navigate(['/broker/dataandproperties']);
  }

  onFileChange(event: any, fieldName: string) {
    if (event.target.files && event.target.files.length) {
      const files = Array.from(event.target.files);
      this.step4Form.patchValue({
        [fieldName]: files,
      });

      console.log(`${fieldName}: ${files.length} files selected`);
    }
  }

  getFileCount(fieldName: string): number {
    const files = this.step4Form.get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  // Check if current form is valid
  isCurrentFormValid(): boolean {
    const currentForm = this.getCurrentForm();

    // For step 0, only check if unit type is selected
    if (this.currentStep === 0) {
      const compoundType = this.step0Form.get('compoundType')?.value;
      const unitType = this.step0Form.get('type')?.value;
      const isValid = !!(compoundType && unitType);
      console.log('Step 0 validation:', { compoundType, unitType, isValid });
      return isValid;
    }

    // For step 2, check only visible/required fields
    if (this.currentStep === 2) {
      return this.isStep2FormValid();
    }

    return currentForm.valid;
  }

  // Custom validation for Step 2 - only check visible fields
  isStep2FormValid(): boolean {
    const form = this.step2Form;
    const fieldsToShow = this.getFieldsToShow();

    // Required fields that must always be valid if they're shown
    const requiredFields = ['unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus'];


    for (const fieldName of requiredFields) {
      if (fieldsToShow.includes(fieldName)) {
        const control = form.get(fieldName);
        if (!control || control.invalid) {
          console.log(`Step 2 validation failed for field: ${fieldName}`, control?.errors);
          return false;
        }
      }
    }

    // Check conditional fields only if they're shown and required for the specific unit type
    const conditionalFields = ['unitFacing', 'legalStatus', 'fitOutCondition', 'furnishingStatus', 'groundLayoutStatus', 'unitDesign', 'activity'];

    for (const fieldName of conditionalFields) {
      if (fieldsToShow.includes(fieldName) && this.isFieldRequiredForUnitType(fieldName)) {
        const control = form.get(fieldName);
        if (!control || control.invalid) {
          console.log(`Step 2 validation failed for conditional field: ${fieldName}`, control?.errors);
          return false;
        }
      }
    }

    console.log('Step 2 validation passed');
    return true;
  }

  // Check if a field is required for the current unit type
  isFieldRequiredForUnitType(fieldName: string): boolean {
    switch (fieldName) {
      case 'unitFacing':
        return this.shouldShowUnitFacingField();
      case 'legalStatus':
        return this.shouldShowLegalStatusField();
      case 'fitOutCondition':
        return this.shouldShowFitOutConditionField();
      case 'furnishingStatus':
        return this.shouldShowFurnishingStatusField();
      case 'groundLayoutStatus':
        return this.shouldShowGroundLayoutStatusField();
      case 'unitDesign':
        return this.shouldShowUnitDesignField();
      case 'activity':
        return this.shouldShowActivityField();
      default:
        return false;
    }
  }

  // Navigate to next step
  nextStep() {
    if (this.currentStep < this.totalSteps - 1) {
      this.currentStep++;
    }
  }

  // Navigate to previous step
  prevStep() {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  formatUnitTypeKey(key: string): string {
    if (!key || typeof key !== 'string') return '';

    return key
      .split('_')
      .map((word) =>
        word.trim() ? word[0].toUpperCase() + word.slice(1).toLowerCase() : ''
      )
      .join(' ');
  }

  toggleAccessory(value: string): void {
    const index = this.selectedAccessories.indexOf(value);

    if (index > -1) {
      this.selectedAccessories.splice(index, 1);
    } else {
      this.selectedAccessories.push(value);
    }

    // Update form control
    this.step2Form.patchValue({
      otherAccessories: [...this.selectedAccessories],
    });
  }

  // Handle "All The Above Are Suitable" checkbox
  onAllAccessoriesChange(event: any): void {
    if (event.target.checked) {
      // Select all accessories
      this.selectedAccessories = this.otherAccessoriesTypes.map((a) => a.value);
    } else {
      // Unselect all accessories
      this.selectedAccessories = [];
    }

    this.step2Form.patchValue({
      otherAccessories: [...this.selectedAccessories],
    });
  }

  isAccessorySelected(value: string): boolean {
    return this.selectedAccessories.includes(value);
  }

  getSelectedAccessoriesText(): string {
    if (this.selectedAccessories.length === 0) {
      return '';
    }

    if (this.selectedAccessories.length === 1) {
      const accessory = this.otherAccessoriesTypes.find(
        (a) => a.value === this.selectedAccessories[0]
      );
      return accessory ? accessory.key : '';
    }

    return `${this.selectedAccessories.length} accessories selected`;
  }

  // Get compound type text for display
  getCompoundTypeText(value: string): string {
    if (!value) return '';
    const option = this.compoundOptions.find(opt => opt.value === value);
    return option ? option.key : '';
  }



  // Get unit type text for display
  getUnitTypeText(value: string): string {
    if (!value) return '';
    const unitType = this.filteredUnitTypes.find(unit => unit.value === value);
    return unitType ? unitType.key : '';
  }
}
