<div class="mb-5 mt-0">
  <app-broker-title [showCreateButton]="false"></app-broker-title>
</div>

<div class="card rounded-4">
  <div class="card-body p-10">
    <div class="stepper stepper-pills d-flex flex-column" id="add_property_stepper">
      <!-- Header and Progress Bar -->
      <div class="mb-5 text-center">
        <ng-container *ngIf="currentStep === 0">
          <h2>
            <span class="text-dark-blue fw-bold">Add Property - </span>
            <span class="text-dark-blue fw-normal">Property Category </span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 1">
          <h2>
            <span class="text-dark-blue fw-bold">Add Unit - </span>
            <span class="text-dark-blue fw-normal">Location Information </span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 2">
          <h2>
            <span class="text-dark-blue fw-bold">Add Unit - </span>
            <span class="text-dark-blue fw-normal">Unit Information </span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 3">
          <h2>
            <span class="text-dark-blue fw-bold">Add Unit - </span>
            <span class="text-dark-blue fw-normal">Payment Details </span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 4">
          <h2>
            <span class="text-dark-blue fw-bold">Add Property - </span>
            <span class="text-dark-blue fw-normal">Media & Documents</span>
          </h2>
        </ng-container>

        <ng-container *ngIf="currentStep === 5">
          <h2>
            <span class="text-dark-blue fw-bold">Add Property - </span>
            <span class="text-dark-blue fw-normal">Review & Submit</span>
          </h2>
        </ng-container>

        <div class="d-flex justify-content-center align-items-center mb-2">
          <span class="text-success fw-bold">Step {{ currentStep }}</span>
          <span class="text-muted mx-1">of</span>
          <span class="text-muted">{{ totalSteps }}</span>
        </div>

        <div *ngIf="currentStep > 0" class="text-primary cursor-pointer mb-2" (click)="prevStep()">
          Back to previous step
        </div>

        <div class="progress h-8px bg-light-success w-75 mx-auto">
          <div class="progress-bar bg-success" role="progressbar" [style.width]="(currentStep / totalSteps) * 100 + '%'"
            aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>

      <!-- Form Content -->
      <form class="mx-auto w-100 pt-5 pb-10">
        <!-- Step 0: Property Category Selection -->
        <div *ngIf="currentStep === 0" [formGroup]="step0Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Compound Type</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="compoundTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getCompoundTypeText(step0Form.get("compoundType")?.value) ||
                  " Select Compound Type"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="compoundTypeDropdown" style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                ">
                <li *ngFor="let option of compoundOptions">
                  <a class="dropdown-item text-start" (click)="selectStep0Value('compoundType', option.value)">{{
                    option.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Unit Type</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="unitTypeDropdownStep0" data-bs-toggle="dropdown" aria-expanded="false"
                [disabled]="filteredUnitTypes.length === 0">
                <span>{{
                  formatUnitTypeKey(step0Form.get("type")?.value) ||
                  "Select Unit Type"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="unitTypeDropdownStep0" style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                ">
                <li *ngFor="let unitType of filteredUnitTypes">
                  <a class="dropdown-item text-start" (click)="selectStep0Value('type', unitType.value)">{{ unitType.key
                    }}</a>
                </li>
              </ul>
            </div>
            <small *ngIf="filteredUnitTypes.length === 0" class="text-muted">
              Please select compound type first
            </small>
          </div>
        </div>

        <!-- Step 1: Basic Property Settings -->
        <div *ngIf="currentStep === 1" [formGroup]="step1Form">
          <!-- Owner Information Row -->
          <div class="row mb-10">
            <!-- Owner Name -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">
                Owner Name
              </label>
              <input type="text" class="form-control text-start" formControlName="ownerName" placeholder="Enter name" />
            </div>

            <!-- Owner Phone -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">
                Owner Phone
              </label>
              <input type="text" class="form-control text-start" [ngClass]="{
                  'is-invalid':
                    step1Form.get('ownerPhone')?.invalid &&
                    (step1Form.get('ownerPhone')?.touched ||
                      step1Form.get('ownerPhone')?.dirty)
                }" formControlName="ownerPhone" placeholder="Enter phone number" />
              <div *ngIf="
                  step1Form.get('ownerPhone')?.invalid &&
                  (step1Form.get('ownerPhone')?.touched ||
                    step1Form.get('ownerPhone')?.dirty)
                " class="invalid-feedback">
                <div *ngIf="step1Form.get('ownerPhone')?.errors?.['required']">
                  Phone number is required.
                </div>
                <div *ngIf="step1Form.get('ownerPhone')?.errors?.['pattern']">
                  Please enter a valid Egyptian phone number (e.g., 01XXXXXXXXX).
                </div>
              </div>
            </div>
          </div>

          <!-- Location Information Row -->
          <div class="row mb-10">
            <!-- City -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">City</label>

              <!-- Loading indicator -->
              <div *ngIf="isLoadingCities" class="text-primary mb-2">
                Loading cities...
              </div>

              <!-- Debug info -->
              <div *ngIf="!isLoadingCities && cities.length === 0" class="text-danger mb-2">
                No cities available
              </div>

              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                  type="button" id="cityDropdownStep1" data-bs-toggle="dropdown" aria-expanded="false"
                  [disabled]="isLoadingCities">
                  <span>
                    <ng-container *ngIf="isLoadingCities">
                      Loading...
                    </ng-container>
                    <ng-container *ngIf="!isLoadingCities">
                      {{ selectedCityName || "Select City" }}
                    </ng-container>
                  </span>
                  <i class="fas fa-chevron-down"></i>
                </button>

                <ul class="dropdown-menu w-100" aria-labelledby="cityDropdownStep1" style="
                    max-height: 200px;
                    overflow-y: auto;
                    position: absolute;
                    z-index: 1000;
                  ">
                  <!-- Debug info -->
                  <li class="dropdown-item disabled">
                    Total - Cities: {{ cities.length }}
                  </li>

                  <ng-container *ngIf="cities && cities.length > 0; else noCities">
                    <li *ngFor="let city of cities" style="cursor: pointer">
                      <a class="dropdown-item text-start" (click)="selectCity(city.id, city.name_en)">
                        {{ city.name_en }}
                      </a>
                    </li>
                  </ng-container>

                  <ng-template #noCities>
                    <li>
                      <a class="dropdown-item text-start disabled">
                        No cities available
                      </a>
                    </li>
                  </ng-template>
                </ul>
              </div>
            </div>

            <!-- Area -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">Area</label>
              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                  type="button" id="areaDropdownStep1" data-bs-toggle="dropdown" aria-expanded="false">
                  <span>{{ selectedAreaName || "Select Area" }}</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu w-100" aria-labelledby="areaDropdownStep1" style="
                    max-height: 200px;
                    overflow-y: auto;
                    position: absolute;
                    z-index: 1000;
                  ">
                  <li *ngIf="areas.length > 0">
                    <a *ngFor="let area of areas" class="dropdown-item text-start"
                      (click)="selectArea(area.id, area.name_en)">{{ area.name_en }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Mall Name -->
          <div class="mb-10" *ngIf="shouldShowField('mallName')">
            <label class="form-label fw-bold text-start d-block">
              Mall Name
            </label>
            <input type="text" class="form-control text-start" formControlName="mallName"
              placeholder="Enter mall name (optional)" [ngClass]="{
                'is-invalid':
                  step1Form.get('mallName')?.invalid &&
                  (step1Form.get('mallName')?.touched ||
                    step1Form.get('mallName')?.dirty)
              }" />
            <div *ngIf="
                step1Form.get('mallName')?.invalid &&
                (step1Form.get('mallName')?.touched ||
                  step1Form.get('mallName')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step1Form.get('mallName')?.errors?.['maxlength']">
                Mall name cannot exceed 255 characters.
              </div>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Detailed Address
            </label>
            <input type="text" class="form-control text-start" formControlName="detailedAddress"
              placeholder="Enter detailed address" />
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Google Maps Link
            </label>
            <input type="text" class="form-control text-start" [ngClass]="{
                'is-invalid':
                  step1Form.get('location')?.invalid &&
                  (step1Form.get('location')?.touched ||
                    step1Form.get('location')?.dirty)
              }" formControlName="location" placeholder="Enter Google Maps link" />
            <div *ngIf="
                step1Form.get('location')?.invalid &&
                (step1Form.get('location')?.touched ||
                  step1Form.get('location')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step1Form.get('location')?.errors?.['required']">
                Google Maps link is required.
              </div>
              <div *ngIf="step1Form.get('location')?.errors?.['pattern']">
                Please enter a valid URL (e.g., https://maps.google.com/...).
              </div>
            </div>
          </div>


        </div>

        <!-- Step 2: Unit Information -->
        <div *ngIf="currentStep === 2" [formGroup]="step2Form">
          <div class="row mb-10">
            <div class="col-md-6" *ngIf="shouldShowField('buildingNumber')">
              <label class="form-label fw-bold text-start d-block">Property Number</label>
              <input type="text" class="form-control text-start" formControlName="buildingNumber"
                placeholder="Enter property number" />
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('unitNumber')">
              <label class="form-label fw-bold text-start d-block">Unit Number</label>
              <input type="text" class="form-control text-start" formControlName="unitNumber"
                placeholder="Enter unit number" />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6" *ngIf="shouldShowField('floor')">
              <label class="form-label fw-bold text-start d-block">Floor</label>
              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                  type="button" id="floorDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  <span>{{
                    formatUnitTypeKey(step2Form.get("floor")?.value) ||
                    "Select floor"
                    }}</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu w-100" aria-labelledby="floorDropdown">
                  <li *ngFor="let floor of floorTypes">
                    <a class="dropdown-item text-start" (click)="selectStep2Value('floor', floor.value)">{{ floor.key
                      }}</a>
                  </li>
                </ul>
              </div>
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('unitArea')">
              <label class="form-label fw-bold text-start d-block">Unit Area (sqm)</label>
              <input type="number" class="form-control text-start" formControlName="unitArea"
                placeholder="Enter area in square meters" min="0" />
            </div>
          </div>

          <!-- Building Area and Ground Area Fields - Only show for specific unit types -->
          <div class="row mb-10" *ngIf="shouldShowAreaFields() ">
            <div class="col-md-6 " *ngIf="shouldShowField('buildingArea')">
              <label class="form-label fw-bold text-start d-block">Building Area (sqm)</label>
              <input type="number" class="form-control text-start" formControlName="buildingArea"
                placeholder="Enter building area in square meters" min="0" />
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('groundArea')">
              <label class="form-label fw-bold text-start d-block">Ground Area (sqm)</label>
              <input type="number" class="form-control text-start" formControlName="groundArea"
                placeholder="Enter ground area in square meters" min="0" />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6" *ngIf="shouldShowField('numberOfRooms')">
              <label class="form-label fw-bold text-start d-block">Number of Rooms</label>
              <input type="number" class="form-control text-start" formControlName="numberOfRooms"
                placeholder="Enter number of rooms" min="0" />
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('numberOfBathrooms')">
              <label class="form-label fw-bold text-start d-block">Number of Bathrooms</label>
              <input type="number" class="form-control text-start" formControlName="numberOfBathrooms"
                placeholder="Enter number of bathrooms" min="0" />
            </div>
          </div>

          <div class="row mb-10" *ngIf="shouldShowField('numberOfFloors')">
            <label class="form-label fw-bold text-start d-block">Number of Floors</label>
            <input type="number" class="form-control text-start" formControlName="numberOfFloors"
              placeholder="Enter number of floors" min="0" />
          </div>

          <!-- Unit Facing Field - Only show for specific unit types -->

          <div class="mb-10" *ngIf="shouldShowUnitFacingField() && shouldShowField('unitFacing')">
            <label class="form-label fw-bold text-start d-block">Apartment Location</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="apartmentLocationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("unitFacing")?.value) ||
                  "Select apartment location"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="apartmentLocationDropdown">
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'right_of_facade')">Right
                    Of Facade</a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'left_of_facade')">
                    Left Of Facade
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'side_view')">
                    Side View
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'rear_view')">
                    Rear View
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Unit Description Field -->
          <div class="mb-10" *ngIf="shouldShowField('unitDescription')">
            <label class="form-label fw-bold text-start d-block">Unit Description</label>
            <textarea class="form-control text-start" formControlName="unitDescription"
              placeholder="Enter unit description (optional)" rows="4" [ngClass]="{
                'is-invalid':
                  step2Form.get('unitDescription')?.invalid &&
                  (step2Form.get('unitDescription')?.touched ||
                    step2Form.get('unitDescription')?.dirty)
              }">
            </textarea>
            <div *ngIf="
                step2Form.get('unitDescription')?.invalid &&
                (step2Form.get('unitDescription')?.touched ||
                  step2Form.get('unitDescription')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step2Form.get('unitDescription')?.errors?.['maxlength']">
                Description cannot exceed 1000 characters.
              </div>
            </div>
            <small class="form-text text-muted">
              Maximum 1000 characters
            </small>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('view')">
            <label class="form-label fw-bold text-start d-block">Apartment View</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="apartmentViewDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("view")?.value) ||
                  "Select apartment view"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="apartmentViewDropdown">
                <li *ngFor="let view of viewTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('view', view.value)">
                    {{ view.key }}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('finishingType')">
            <label class="form-label fw-bold text-start d-block">Finishing Status</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="finishingStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("finishingType")?.value) ||
                  "Select finishing status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="finishingStatusDropdown">
                <li *ngFor="let status of finishingTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('finishingType', status.value)">{{
                    status.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Fit Out Condition Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowFitOutConditionField() && shouldShowField('fitOutCondition')">
            <label class="form-label fw-bold text-start d-block">Fit Out Condition Status</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="fitOutConditionDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("fitOutCondition")?.value) ||
                  "Select fit out condition"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="fitOutConditionDropdown">
                <li *ngFor="let condition of fitOutConditionTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('fitOutCondition', condition.value)
                    ">{{ condition.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Furnishing Status Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowFurnishingStatusField() && shouldShowField('furnishingStatus')">
            <label class="form-label fw-bold text-start d-block">Furnishing Status</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="furnishingStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("furnishingStatus")?.value) ||
                  "Select furnishing status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="furnishingStatusDropdown">
                <li *ngFor="let furnishing of furnishingStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('furnishingStatus', furnishing.value)
                    ">{{ furnishing.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Ground Layout Status Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowGroundLayoutStatusField() && shouldShowField('groundLayoutStatus')">
            <label class="form-label fw-bold text-start d-block">Ground Layout Status</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="groundLayoutStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(
                  step2Form.get("groundLayoutStatus")?.value
                  ) || "Select ground layout status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="groundLayoutStatusDropdown">
                <li *ngFor="let layout of groundLayoutStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('groundLayoutStatus', layout.value)
                    ">{{ layout.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Unit Design Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowUnitDesignField() && shouldShowField('unitDesign')">
            <label class="form-label fw-bold text-start d-block">Unit Design</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="unitDesignDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("unitDesign")?.value) ||
                  "Select unit design"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="unitDesignDropdown">
                <li *ngFor="let design of unitDesignTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitDesign', design.value)">{{
                    design.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Activity Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowActivityField() && shouldShowField('activity')">
            <label class="form-label fw-bold text-start d-block">Activity</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="activityDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("activity")?.value) ||
                  "Select activity"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="activityDropdown">
                <li *ngFor="let activity of activityTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('activity', activity.value)">{{
                    activity.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('deliveryStatus')">
            <label class="form-label fw-bold text-start d-block">Delivery Status</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="deliveryStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("deliveryStatus")?.value) ||
                  "Select delivery status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="deliveryStatusDropdown">
                <li *ngFor="let delivery of deliveryTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('deliveryStatus', delivery.value)">{{
                    delivery.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('deliveryDate')">
            <label class="form-label fw-bold text-start d-block">Delivery Date</label>
            <input type="date" class="form-control text-start" formControlName="deliveryDate"
              placeholder="Select delivery date" />
          </div>

          <!-- Legal Status Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowLegalStatusField() && shouldShowField('legalStatus')">
            <label class="form-label fw-bold text-start d-block">
              Legal Status
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="legalStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("legalStatus")?.value) ||
                  "Select legal status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="legalStatusDropdown">
                <li *ngFor="let legal of legalTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('legalStatus', legal.value)">{{
                    legal.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Financial Status Field -->
          <div class="mb-10" *ngIf="shouldShowField('financialStatus')">
            <label class="form-label fw-bold text-start d-block">
              Financial Status
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="financialStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("financialStatus")?.value) ||
                  "Select financial status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="financialStatusDropdown">
                <li *ngFor="let financial of financialStatusTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('financialStatus', financial.value)">{{
                    financial.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('otherAccessories')">
            <label class="form-label fw-bold text-start d-block">
              Other Accessories</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="additionalAmenitiesDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getSelectedAccessoriesText() || "Select additional amenities"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100 p-3" aria-labelledby="additionalAmenitiesDropdown">
                <!-- All The Above Are Suitable - Special handling -->
                <li class="mb-2">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="accessory_alltheabovearesuitable" [checked]="
                        isAccessorySelected('all_the_above_are_suitable')
                      " (change)="onAllAccessoriesChange($event)" />
                    <label class="form-check-label text-start" for="accessory_alltheabovearesuitable">
                      All The Above Are Suitable
                    </label>
                  </div>
                </li>

                <hr class="my-2" />

                <!-- Individual accessories -->
                <li *ngFor="let accessory of otherAccessoriesTypes" class="mb-2">
                  <div class="form-check" *ngIf="accessory.value !== 'alltheabovearesuitable'">
                    <input class="form-check-input" type="checkbox" [id]="'accessory_' + accessory.value"
                      [checked]="isAccessorySelected(accessory.value)" (change)="toggleAccessory(accessory.value)" />
                    <label class="form-check-label text-start" [for]="'accessory_' + accessory.value">
                      {{ accessory.key }}
                    </label>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Step 3: Project type   -->
        <div *ngIf="currentStep === 3" [formGroup]="step3Form">
          <!-- Payment System -->
          <div class="mb-10" *ngIf="shouldShowField('paymentSystem')">
            <label class="form-label fw-bold text-start d-block">
              Payment System
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="paymentSystemDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step3Form.get("paymentSystem")?.value) ||
                  "Select payment system"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="paymentSystemDropdown">
                <li *ngFor="let payment of paymentTypes">
                  <a class="dropdown-item text-start" (click)="selectStep3Value('paymentSystem', payment.value)">{{
                    payment.key }}</a>
                </li>
              </ul>
            </div>
          </div>


          <!-- Cash Price Fields - Show when Cash is selected -->
          <ng-container *ngIf="shouldShowCashFields() && shouldShowField('pricePerMeterInCash')">
            <div class="mb-10">
              <label class="form-label fw-bold text-start d-block">Price Per Meter In Cash</label>
              <input type="number" class="form-control text-start" formControlName="pricePerMeterInCash"
                placeholder="Enter price per meter in cash" min="0" />
            </div>

            <div class="mb-10">
              <label class="form-label fw-bold text-start d-block">Total Price In Cash</label>
              <input type="number" class="form-control text-start" formControlName="totalPriceInCash"
                placeholder="Enter total price in cash" min="0" />
            </div>
          </ng-container>

          <!-- Installment Price Fields - Show when Installment is selected -->
          <ng-container *ngIf="shouldShowInstallmentFields() && shouldShowField('pricePerMeterInInstallment')">
            <div class="mb-10">
              <label class="form-label fw-bold text-start d-block">Price Per Meter In Installment</label>
              <input type="number" class="form-control text-start" formControlName="pricePerMeterInInstallment"
                placeholder="Enter price per meter in installment" min="0" />
            </div>

            <div class="mb-10" *ngIf="shouldShowField('totalPriceInInstallment')">
              <label class="form-label fw-bold text-start d-block">Total Price In Installment</label>
              <input type="number" class="form-control text-start" formControlName="totalPriceInInstallment"
                placeholder="Enter total price in installment" min="0" />
            </div>
          </ng-container>
        </div>

        <!-- Step 4: Project Documents -->
        <div *ngIf="currentStep === 4" [formGroup]="step4Form">
          <!-- Project Documents Cards -->
          <div class="mb-10 upload-card-container">
            <!--    Upload image of main unit -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectUnitImage" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload image of main unit
                  <span *ngIf="getFileCount('diagram') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("diagram") }}
                  </span>
                </span>
                <input type="file" id="projectUnitImage" class="d-none" (change)="onFileChange($event, 'diagram')"
                  multiple />
              </label>
            </div>

            <!-- Upload a photos to the gallery -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectLayout" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload photos to the gallery
                  <span *ngIf="getFileCount('layout') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("layout") }}
                  </span>
                </span>
                <input type="file" id="projectLayout" class="d-none" (change)="onFileChange($event, 'layout')"
                  multiple />
              </label>
            </div>
            <!-- Project Videos -->
            <div class="card mb-5 cursor-pointer">
              <label for="videos" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload project videos

                  <span *ngIf="getFileCount('videos') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("videos") }}
                  </span>
                </span>
                <input type="file" id="videos" class="d-none" (change)="onFileChange($event, 'videos')" accept="video/*"
                  multiple />
              </label>
            </div>

            <!-- Upload unit plan -->
            <div class="card mb-5 cursor-pointer">
              <label for="locationInMasterPlan" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload unit plan
                  <span *ngIf="getFileCount('locationInMasterPlan') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("locationInMasterPlan") }}
                  </span>
                </span>
                <input type="file" id="locationInMasterPlan" class="d-none"
                  (change)="onFileChange($event, 'locationInMasterPlan')" multiple />
              </label>
            </div>
          </div>
        </div>

        <!-- Step 5: Review & Submit -->
        <div *ngIf="currentStep === 5" [formGroup]="step5Form">
          <div class="text-center py-5">
            <div class="mb-4">
              <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
            </div>
            <h4 class="mb-3">Ready to Submit Property</h4>
            <p class="text-muted mb-4">
              Please review all the information you've entered and click submit to add the property.
            </p>
            <div class="alert alert-info mb-4">
              <strong>Note:</strong>
              <ul class="list-unstyled mb-0 mt-2">
                <li>• Owner information is now collected in Step 1</li>
                <li>• Legal Status is now part of Step 2 (Unit Information)</li>
                <li>• All forms have been reorganized for better user experience</li>
              </ul>
            </div>

            <!-- Submit Buttons -->
            <div class="d-flex flex-column gap-3 mt-4">
              <button type="button" class="btn btn-lg btn-success px-10 py-3 rounded-pill"
                [disabled]="!isCurrentFormValid()" (click)="submitForm(false)">
                <span class="indicator-label"> Add Property</span>
              </button>

              <button type="button" class="btn btn-primary btn-lg px-10 py-3 rounded-pill" [disabled]="!step5Form.valid"
                (click)="submitForm(true)">
                <span class="indicator-label">
                  <i class="fas fa-bullhorn me-2"></i> Add Property & Publish
                </span>
                <span class="indicator-progress">
                  Please wait...
                  <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
              </button>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->

        <!-- Step 0 Navigation -->
        <div *ngIf="currentStep === 0" class="d-flex justify-content-between pt-10">
          <button type="button" class="btn btn-light-dark btn-lg px-6 py-3" (click)="cancel()">
            Cancel
          </button>

          <button type="button" class="btn btn-lg btn-navy px-10 py-3 rounded-pill" [disabled]="!isCurrentFormValid()"
            (click)="nextStep()">
            <span class="indicator-label text-white"> Next - Location Information </span>
          </button>
        </div>

        <!-- Step 1 Navigation -->
        <div *ngIf="currentStep === 1" class="d-flex justify-content-center pt-10">
          <button type="button" class="btn btn-lg btn-navy px-10 py-3 rounded-pill" [disabled]="!isCurrentFormValid()"
            (click)="nextStep()">
            <span class="indicator-label text-white"> Next - Unit Data </span>
          </button>
        </div>

        <!-- ******-->
        <div *ngIf="currentStep > 1" class="d-flex justify-content-center pt-10">
          <ng-container *ngIf="currentStep < 5">
            <button type="button" class="btn btn-lg btn-navy px-10 py-3 rounded-pill" [disabled]="!isCurrentFormValid()"
              (click)="nextStep()">
              <span class="indicator-label text-white">
                <ng-container *ngIf="currentStep === 2">
                  Next - Payment Details
                </ng-container>
                <ng-container *ngIf="currentStep === 3">
                  Next - Media & Documents
                </ng-container>
                <ng-container *ngIf="currentStep === 4">
                  Next - Review & Submit
                </ng-container>
              </span>
            </button>
          </ng-container>


        </div>
      </form>
    </div>
  </div>
</div>